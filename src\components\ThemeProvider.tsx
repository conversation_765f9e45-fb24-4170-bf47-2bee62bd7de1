'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { Theme, ResolvedTheme } from '@/hooks/useTheme';

interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useThemeContext = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>('system');
  const [resolvedTheme, setResolvedTheme] = useState<ResolvedTheme>('light');
  const [isLoading, setIsLoading] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // 获取系统主题偏好
  const getSystemTheme = (): ResolvedTheme => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  // 解析主题
  const resolveTheme = (theme: Theme): ResolvedTheme => {
    if (theme === 'system') {
      return getSystemTheme();
    }
    return theme;
  };

  // 应用主题到DOM
  const applyTheme = (resolvedTheme: ResolvedTheme) => {
    if (typeof window === 'undefined') return;
    
    try {
      const root = document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(resolvedTheme);
      root.setAttribute('data-theme', resolvedTheme);

      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#111827' : '#ffffff');
      }
    } catch (error) {
      console.warn('Failed to apply theme:', error);
    }
  };

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('theme', newTheme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }

    const resolved = resolveTheme(newTheme);
    setResolvedTheme(resolved);
    
    if (isMounted) {
      applyTheme(resolved);
    }
  };

  // 切换主题
  const toggleTheme = () => {
    const themeOrder: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    setTheme(themeOrder[nextIndex]);
  };

  // 初始化主题
  useEffect(() => {
    setIsMounted(true);

    if (typeof window === 'undefined') return;

    try {
      // 使用脚本预设的主题信息
      const initialTheme = window.__INITIAL_THEME__;

      if (initialTheme) {
        setThemeState(initialTheme.theme);
        setResolvedTheme(initialTheme.resolvedTheme);
        applyTheme(initialTheme.resolvedTheme);
      } else {
        // 回退到手动检测
        const savedTheme = localStorage.getItem('theme') as Theme;
        const validThemes: Theme[] = ['light', 'dark', 'system'];

        if (savedTheme && validThemes.includes(savedTheme)) {
          setThemeState(savedTheme);
          const resolved = resolveTheme(savedTheme);
          setResolvedTheme(resolved);
          applyTheme(resolved);
        } else {
          const systemTheme = getSystemTheme();
          setResolvedTheme(systemTheme);
          applyTheme(systemTheme);
        }
      }
    } catch (error) {
      console.warn('Failed to initialize theme:', error);
    }

    setIsLoading(false);
  }, []);

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined' || !isMounted) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        const resolved = getSystemTheme();
        setResolvedTheme(resolved);
        applyTheme(resolved);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [theme, isMounted]);

  const value: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
    isLoading
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}
