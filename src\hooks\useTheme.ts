'use client';

import { useState, useEffect, useCallback } from 'react';

// 主题类型定义
export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

// 主题Hook返回类型
interface UseThemeReturn {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isLoading: boolean;
}

// 获取系统主题偏好
const getSystemTheme = (): ResolvedTheme => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 解析主题（将system转换为实际的light/dark）
const resolveTheme = (theme: Theme): ResolvedTheme => {
  if (theme === 'system') {
    return getSystemTheme();
  }
  return theme;
};

// 应用主题到DOM
const applyTheme = (resolvedTheme: ResolvedTheme) => {
  // 确保在客户端环境中执行
  if (typeof window === 'undefined') return;

  try {
    const root = document.documentElement;

    // 移除所有主题类
    root.classList.remove('light', 'dark');

    // 添加新主题类
    root.classList.add(resolvedTheme);

    // 设置data属性（用于CSS选择器）
    root.setAttribute('data-theme', resolvedTheme);

    // 更新meta标签（用于移动端状态栏）
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#111827' : '#ffffff');
    }
  } catch (error) {
    console.warn('Failed to apply theme:', error);
  }
};

// 主题管理Hook
export const useTheme = (): UseThemeReturn => {
  const [theme, setThemeState] = useState<Theme>('system');
  const [resolvedTheme, setResolvedTheme] = useState<ResolvedTheme>('light');
  const [isLoading, setIsLoading] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // 从localStorage加载主题
  const loadTheme = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      const savedTheme = localStorage.getItem('theme') as Theme;
      const validThemes: Theme[] = ['light', 'dark', 'system'];

      if (savedTheme && validThemes.includes(savedTheme)) {
        setThemeState(savedTheme);
      } else {
        setThemeState('system');
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
      setThemeState('system');
    }
  }, []);

  // 保存主题到localStorage
  const saveTheme = useCallback((newTheme: Theme) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('theme', newTheme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  }, []);

  // 更新解析后的主题
  const updateResolvedTheme = useCallback((currentTheme: Theme) => {
    const resolved = resolveTheme(currentTheme);
    setResolvedTheme(resolved);

    // 应用主题到DOM（只在客户端且组件已挂载时）
    if (typeof window !== 'undefined' && isMounted) {
      requestAnimationFrame(() => {
        applyTheme(resolved);
      });
    }
  }, [isMounted]);

  // 设置主题
  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    saveTheme(newTheme);
    updateResolvedTheme(newTheme);
  }, [saveTheme, updateResolvedTheme]);

  // 切换主题（在light、dark、system之间循环）
  const toggleTheme = useCallback(() => {
    const themeOrder: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    setTheme(themeOrder[nextIndex]);
  }, [theme, setTheme]);

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        updateResolvedTheme('system');
      }
    };

    // 添加监听器
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme, updateResolvedTheme]);

  // 组件挂载状态管理
  useEffect(() => {
    setIsMounted(true);
    loadTheme();
  }, [loadTheme]);

  // 当主题状态改变时更新解析后的主题
  useEffect(() => {
    if (isMounted) {
      updateResolvedTheme(theme);
      setIsLoading(false);
    }
  }, [theme, updateResolvedTheme, isMounted]);

  // 初始化时立即应用主题，防止闪烁
  useEffect(() => {
    if (typeof window === 'undefined' || !isMounted) return;

    // 检查主题脚本是否已执行
    if (window.__THEME_SCRIPT_EXECUTED__) {
      // 如果主题脚本已执行，只需要同步状态
      setIsLoading(false);
      return;
    }

    // 立即应用当前主题
    const currentResolved = resolveTheme(theme);
    applyTheme(currentResolved);

    // 添加过渡动画类
    try {
      document.documentElement.style.setProperty(
        '--theme-transition',
        'background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease'
      );
    } catch (error) {
      console.warn('Failed to set theme transition:', error);
    }

    // 移除加载状态
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, [theme, isMounted]);

  return {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
    isLoading
  };
};
