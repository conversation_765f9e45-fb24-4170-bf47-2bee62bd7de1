// 防止主题闪烁的内联脚本组件
// 这个脚本只设置标记，不直接操作DOM

export default function ThemeScript() {
  const themeScript = `
    (function() {
      try {
        // 只设置标记，让React组件处理DOM操作
        window.__THEME_SCRIPT_EXECUTED__ = false;

        // 获取保存的主题信息，供React组件使用
        var savedTheme = localStorage.getItem('theme');
        var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        var theme = savedTheme || 'system';
        var resolvedTheme = theme === 'system' ? systemTheme : theme;

        // 将主题信息存储到window对象，供React组件使用
        window.__INITIAL_THEME__ = {
          theme: theme,
          resolvedTheme: resolvedTheme
        };

      } catch (error) {
        console.warn('Theme script error:', error);
        // 出错时设置默认值
        window.__INITIAL_THEME__ = {
          theme: 'light',
          resolvedTheme: 'light'
        };
        window.__THEME_SCRIPT_EXECUTED__ = false;
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: themeScript
      }}
    />
  );
}
