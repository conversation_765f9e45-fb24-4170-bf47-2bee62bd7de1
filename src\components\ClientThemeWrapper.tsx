'use client';

import { useEffect, useState } from 'react';

interface ClientThemeWrapperProps {
  children: React.ReactNode;
}

export default function ClientThemeWrapper({ children }: ClientThemeWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 在服务端渲染时，返回一个没有主题类的版本
  if (!isMounted) {
    return <>{children}</>;
  }

  // 客户端渲染时，返回完整的内容
  return <>{children}</>;
}
